/**
 * AI-Powered Intelligent Column Mapping Service
 * Uses OpenAI GPT to analyze column headers and cell content for 100% accurate mapping
 */

import { CATEGORIES } from '@/data/categoryData';

export interface ColumnAnalysis {
  originalColumn: string;
  targetField: string;
  confidence: number;
  reasoning: string;
  sampleValues: string[];
}

export interface AIColumnMappingResult {
  mappings: ColumnAnalysis[];
  suggestedCategory: string;
  suggestedSubcategory: string;
  unmappedColumns: string[];
  confidence: number;
}

/**
 * Target fields in our product system
 */
export const TARGET_FIELDS = {
  // Core identification
  partArticleNumber: 'Part Article Number - Primary product identifier',
  name: 'Product Name - Main product title/description',
  sku: 'SKU - Stock Keeping Unit identifier',
  
  // Business information
  manufacturer: 'Manufacturer - Brand or company that makes the product',
  supplierName: 'Supplier Name - Company/vendor supplying the product',
  stockQuantity: 'Stock Quantity - Number of units available',
  retailPrice: 'Retail Price - Selling price for merchants',
  wholesalePrice: 'Wholesale Price - Bulk pricing for suppliers',
  
  // Categorization
  category: 'Category - Main product category',
  subcategory: 'Subcategory - Specific product subcategory',
  
  // Content and specifications
  descriptionAndSpecifications: 'Description - Detailed product description and specifications',
  
  // Images
  primaryImage: 'Primary Image - Main product image URL',
  additionalImages: 'Additional Images - Array of additional image URLs',
  
  // Logistics
  shippingOrigin: 'Shipping Origin - Location where product ships from',
  
  // Vehicle compatibility (converted to text input)
  vehicleCompatibility: 'Vehicle Compatibility - Compatible vehicle types (text input)',
  
  // System fields
  status: 'Status - Product status (draft, active, out_of_stock, discontinued)',
  
  // Category-specific fields for tyres
  width: 'Tyre Width - Width measurement for tyres',
  aspectRatio: 'Aspect Ratio - Aspect ratio for tyres',
  rimDiameter: 'Rim Diameter - Rim diameter for tyres',
  loadIndex: 'Load Index - Load index for tyres',
  speedRating: 'Speed Rating - Speed rating for tyres',
  season: 'Season - Tyre season type (summer, winter, all-season)'
};

/**
 * Get available categories and subcategories for AI analysis
 */
export const getAvailableCategories = () => {
  return CATEGORIES.map(cat => ({
    id: cat.id,
    name: cat.name,
    displayName: cat.displayName,
    description: cat.description,
    subcategories: cat.subcategories.map(sub => ({
      id: sub.id,
      name: sub.name,
      displayName: sub.displayName
    }))
  }));
};

/**
 * Prepare optimized data for AI analysis (reduced token consumption)
 */
export const prepareDataForAI = (data: any[]): { headers: string[], sampleData: any[] } => {
  if (data.length === 0) return { headers: [], sampleData: [] };

  const headers = Object.keys(data[0]);
  // Reduce sample data to 2 rows maximum for token efficiency
  const sampleData = data.slice(0, Math.min(2, data.length));

  return { headers, sampleData };
};

// Token-optimized mapping cache
export const mappingCache = new Map<string, AIColumnMappingResult>();

/**
 * Generate cache key from headers
 */
export const getCacheKey = (headers: string[]): string => {
  return headers.sort().join('|').toLowerCase();
};

/**
 * Estimate token count (rough approximation: 1 token ≈ 4 characters)
 */
export const estimateTokens = (text: string): number => {
  return Math.ceil(text.length / 4);
};

/**
 * Validate category and subcategory against available options
 */
export const validateCategoryMapping = (category: string, subcategory: string): { validCategory: string; validSubcategory: string } => {
  const availableCategories = getAvailableCategories();

  // Find exact match first
  let validCategory = availableCategories.find(cat => cat.id === category)?.id;

  // If no exact match, try fuzzy matching
  if (!validCategory) {
    validCategory = availableCategories.find(cat =>
      cat.name.toLowerCase().includes(category.toLowerCase()) ||
      category.toLowerCase().includes(cat.name.toLowerCase())
    )?.id;
  }

  // Default to brakes if no match found
  if (!validCategory) {
    validCategory = 'brakes';
  }

  // Validate subcategory
  const categoryData = availableCategories.find(cat => cat.id === validCategory);
  let validSubcategory = categoryData?.subcategories.find(sub => sub.id === subcategory)?.id;

  // If no exact subcategory match, try fuzzy matching
  if (!validSubcategory && categoryData) {
    validSubcategory = categoryData.subcategories.find(sub =>
      sub.name.toLowerCase().includes(subcategory.toLowerCase()) ||
      subcategory.toLowerCase().includes(sub.name.toLowerCase())
    )?.id;
  }

  // Default to first subcategory if no match
  if (!validSubcategory && categoryData?.subcategories.length > 0) {
    validSubcategory = categoryData.subcategories[0].id;
  }

  return { validCategory, validSubcategory: validSubcategory || '' };
};

/**
 * Generate intelligent product name from row data
 */
export const generateProductName = (rowData: any, mappings: ColumnAnalysis[]): string => {
  const getValue = (targetField: string): string => {
    const mapping = mappings.find(m => m.targetField === targetField);
    return mapping ? String(rowData[mapping.originalColumn] || '') : '';
  };

  const brand = getValue('manufacturer') || getValue('supplierName');
  const type = getValue('name') || getValue('category');
  const vehicle = getValue('vehicleCompatibility');
  const partNumber = getValue('partArticleNumber');

  // Build intelligent name
  let name = '';
  if (brand) name += brand + ' ';
  if (type) name += type;
  if (vehicle) name += ` for ${vehicle}`;
  if (!name && partNumber) name = `Part ${partNumber}`;
  if (!name) name = 'Automotive Part';

  return name.trim();
};

/**
 * Generate enhanced product description from all available data
 */
export const generateEnhancedDescription = (rowData: any, mappings: ColumnAnalysis[], unmappedColumns: string[]): string => {
  const sections: string[] = [];

  // Add mapped specifications
  const specs = mappings
    .filter(m => m.targetField === 'descriptionAndSpecifications')
    .map(m => `${m.originalColumn}: ${rowData[m.originalColumn]}`)
    .filter(spec => spec.split(': ')[1]); // Only include non-empty values

  if (specs.length > 0) {
    sections.push('Specifications:\n' + specs.join('\n'));
  }

  // Add unmapped data in organized format
  const unmappedData = unmappedColumns
    .map(col => ({ column: col, value: rowData[col] }))
    .filter(item => item.value && String(item.value).trim())
    .map(item => `${item.column}: ${item.value}`);

  if (unmappedData.length > 0) {
    sections.push('Additional Information:\n' + unmappedData.join('\n'));
  }

  return sections.join('\n\n');
};

/**
 * Smart category detection based on product type analysis
 */
export const detectCategoryFromData = (headers: string[], sampleData: any[]): { category: string; subcategory: string; confidence: number } => {
  const allText = sampleData.map(row =>
    headers.map(h => String(row[h] || '')).join(' ')
  ).join(' ').toLowerCase();

  // Category detection patterns with confidence scoring
  const categoryPatterns = [
    { category: 'brakes', subcategory: 'brake-disc', patterns: ['brake disc', 'brake disk', 'rotor'], confidence: 95 },
    { category: 'brakes', subcategory: 'brake-pads', patterns: ['brake pad', 'brake pads', 'pad set'], confidence: 95 },
    { category: 'filters', subcategory: 'oil-filters', patterns: ['oil filter', 'filter oil'], confidence: 95 },
    { category: 'filters', subcategory: 'air-filters', patterns: ['air filter', 'filter air'], confidence: 95 },
    { category: 'filters', subcategory: 'fuel-filters', patterns: ['fuel filter', 'filter fuel'], confidence: 95 },
    { category: 'damping', subcategory: 'shock-absorber', patterns: ['shock absorber', 'damper', 'strut'], confidence: 90 },
    { category: 'engine', subcategory: 'spark-plugs', patterns: ['spark plug', 'ignition plug'], confidence: 95 },
    { category: 'tyres', subcategory: 'tyres', patterns: ['tyre', 'tire', 'wheel'], confidence: 90 },
    { category: 'oils-fluids', subcategory: 'engine-lubrication-oil', patterns: ['engine oil', 'motor oil', 'lubricant'], confidence: 90 }
  ];

  let bestMatch = { category: 'brakes', subcategory: 'brake-disc', confidence: 50 };

  for (const pattern of categoryPatterns) {
    for (const searchPattern of pattern.patterns) {
      if (allText.includes(searchPattern)) {
        if (pattern.confidence > bestMatch.confidence) {
          bestMatch = pattern;
        }
      }
    }
  }

  return bestMatch;
};

/**
 * Enhanced AI prompt with accurate category mapping (optimized for accuracy + tokens)
 */
export const createAIPrompt = (headers: string[], sampleData: any[]): string => {
  // Use first 2 rows for better context, key values only (first 10 chars)
  const samples = sampleData.slice(0, 2).map((row, i) =>
    `R${i + 1}:${headers.map(h => `${h}:${String(row[h] || '').slice(0, 10)}`).join(',')}`
  ).join('|');

  // Smart category detection for better accuracy
  const detectedCategory = detectCategoryFromData(headers, sampleData);

  // Get top 5 most relevant categories to reduce token usage
  const availableCategories = getAvailableCategories();
  const topCategories = availableCategories
    .filter(cat => cat.id !== 'all')
    .slice(0, 5)
    .map(cat => `${cat.id}(${cat.subcategories.slice(0, 2).map(sub => sub.id).join(',')})`);

  return `Map CSV to fields: partArticleNumber,name,manufacturer,category,subcategory,vehicleCompatibility,descriptionAndSpecifications,retailPrice,supplierName,stockQuantity,minimumOrderQuantity,deliveryTime,weight,dimensions,oeNumbers,images. Likely category: ${detectedCategory.category}. Available: ${topCategories.join(',')}. Headers:${headers.join(',')}. Data:${samples}. JSON:{mappings:[{col,field,conf}],category,subcategory,unmapped:[],confidence}`;
};

/**
 * Analyze columns using AI with production backend service
 */
export const analyzeColumnsWithAI = async (
  data: any[],
  apiKey?: string
): Promise<AIColumnMappingResult> => {
  try {
    // Import the backend service dynamically to avoid circular dependencies
    const { analyzeColumnsWithProductionAI } = await import('./aiMappingBackend');

    console.log('🤖 Using production AI mapping service...');
    return await analyzeColumnsWithProductionAI(data);

  } catch (error) {
    console.error('❌ AI column mapping failed:', error);

    // Fallback to pattern matching
    console.log('🔄 Falling back to pattern matching...');
    const { headers, sampleData } = prepareDataForAI(data);
    const fallbackResult = fallbackColumnMapping(headers, sampleData);

    console.log('✅ Fallback mapping completed');
    return fallbackResult;
  }
};

/**
 * Legacy function for direct API calls (kept for compatibility)
 */
export const analyzeColumnsWithDirectAPI = async (
  headers: string[],
  sampleData: any[],
  apiKey: string
): Promise<AIColumnMappingResult> => {
  const prompt = createAIPrompt(headers, sampleData);

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert data analyst specializing in automotive parts data mapping. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    const aiResponse = result.choices[0]?.message?.content;

    if (!aiResponse) {
      throw new Error('No response from OpenAI API');
    }

    // Parse the JSON response
    const parsedResult: AIColumnMappingResult = JSON.parse(aiResponse);

    // Validate the response structure
    if (!parsedResult.mappings || !Array.isArray(parsedResult.mappings)) {
      throw new Error('Invalid AI response structure');
    }

    return parsedResult;

  } catch (error) {
    console.error('AI column mapping error:', error);
    throw new Error(`AI analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Enhanced fallback mapping with smart category detection
 */
export const fallbackColumnMapping = (headers: string[], sampleData: any[]): AIColumnMappingResult => {
  const mappings: ColumnAnalysis[] = [];
  const unmappedColumns: string[] = [];

  // Use smart category detection for fallback
  const detectedCategory = detectCategoryFromData(headers, sampleData);
  
  // Enhanced pattern matching rules
  const patterns = {
    partArticleNumber: [
      /^(vendor|supplier|item|part.*number|article.*number|sku|product.*code|reference|ref)$/i,
      /^(part.*article|article.*part|item.*code|product.*id)$/i
    ],
    manufacturer: [
      /^(brand|manufacturer|make|producer|company)$/i,
      /^(brand.*name|manufacturer.*name|make.*name)$/i
    ],
    name: [
      /^(name|title|product.*name|item.*name|description|product.*title)$/i,
      /^(product.*description|item.*description)$/i
    ],
    supplierName: [
      /^(supplier|vendor|distributor|wholesaler)$/i,
      /^(supplier.*name|vendor.*name)$/i
    ],
    retailPrice: [
      /^(price|retail.*price|unit.*price|selling.*price|list.*price)$/i,
      /^(netto.*price|net.*price|cost)$/i
    ],
    stockQuantity: [
      /^(stock|quantity|qty|inventory|available|on.*hand)$/i,
      /^(stock.*quantity|available.*quantity)$/i
    ],
    vehicleCompatibility: [
      /^(vehicle|car|auto|compatibility|application|fits)$/i,
      /^(vehicle.*type|car.*type|auto.*type)$/i
    ]
  };

  headers.forEach(header => {
    let mapped = false;
    const cleanHeader = header.toLowerCase().trim();
    
    for (const [targetField, regexList] of Object.entries(patterns)) {
      if (regexList.some(regex => regex.test(cleanHeader))) {
        const sampleValues = sampleData.slice(0, 3).map(row => row[header]?.toString() || '');
        mappings.push({
          originalColumn: header,
          targetField,
          confidence: 75,
          reasoning: `Pattern match for ${targetField}`,
          sampleValues
        });
        mapped = true;
        break;
      }
    }
    
    if (!mapped) {
      unmappedColumns.push(header);
    }
  });

  // Validate detected category
  const { validCategory, validSubcategory } = validateCategoryMapping(
    detectedCategory.category,
    detectedCategory.subcategory
  );

  return {
    mappings,
    suggestedCategory: validCategory,
    suggestedSubcategory: validSubcategory,
    unmappedColumns,
    confidence: Math.max(60, detectedCategory.confidence - 10) // Slightly lower confidence for fallback
  };
};
