
import React, { useState, useEffect, useRef } from 'react';
import { useLocation, NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Package, ShoppingBag, Truck, Users, Settings,
  BarChart, Home, LogOut, Tag, Gauge, Link,
  ChevronRight, ChevronDown, ChevronUp, Menu as MenuIcon, X as XIcon
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';
import { LogoutButton } from '@/components/common/LogoutConfirmationModal';
import { useDashboard } from '@/hooks/useDashboardData';



interface SidebarItemProps {
  icon: React.ElementType;
  label: string;
  href: string;
  badge?: string | number;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
  children?: React.ReactNode;
  collapsed?: boolean;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
}

const SidebarItem = ({
  icon: Icon,
  label,
  href,
  badge,
  badgeVariant = 'default',
  children,
  collapsed = false,
  onClick
}: SidebarItemProps) => {
  const location = useLocation();
  const [open, setOpen] = useState(false);
  const isActive = location.pathname === href || (href !== '/' && location.pathname.startsWith(href));
  const hasChildren = Boolean(children);

  // Open submenu if active path is inside
  useEffect(() => {
    if (hasChildren && !open && location.pathname.startsWith(href)) {
      setOpen(true);
    }
  }, [location.pathname, hasChildren, href, open]);

  const handleClick = (e: React.MouseEvent) => {
    if (hasChildren) {
      e.preventDefault();
      setOpen(!open);
    }
  };

  // Main menu item
  const menuItem = (
    <div
      className={cn(
        "flex items-center justify-between w-full px-3 py-2.5 rounded-md cursor-pointer",
        "transition-colors duration-200 ease-in-out group",
        isActive
          ? "bg-primary/10 text-primary font-medium"
          : "text-foreground/70 hover:bg-primary/5 hover:text-primary"
      )}
      onClick={handleClick}
    >
      <div className="flex items-center gap-3 min-w-0">
        <div className={cn(
          "flex items-center justify-center w-5 h-5",
          isActive ? "text-primary" : "text-foreground/60 group-hover:text-primary"
        )}>
          <Icon size={18} />
        </div>

        {!collapsed && (
          <span className="truncate">{label}</span>
        )}
      </div>

      {!collapsed && (
        <div className="flex items-center gap-2">
          {badge && (
            <Badge variant={badgeVariant} className="h-5 px-1.5 text-xs font-medium">
              {badge}
            </Badge>
          )}

          {hasChildren && (
            <div className="text-foreground/50">
              {open ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </div>
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className="w-full">
      {hasChildren ? (
        <div className="w-full">
          {collapsed ? (
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <NavLink to={href} className="block" onClick={onClick}>
                    {menuItem}
                  </NavLink>
                </TooltipTrigger>
                <TooltipContent side="right" className="flex items-center gap-2">
                  {label}
                  {badge && (
                    <Badge variant={badgeVariant} className="h-5 px-1.5 text-xs font-medium">
                      {badge}
                    </Badge>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            menuItem
          )}

          {/* Submenu */}
          {!collapsed && open && (
            <div className="mt-1 ml-4 pl-3 border-l border-border/40 space-y-1">
              {children}
            </div>
          )}
        </div>
      ) : (
        collapsed ? (
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <NavLink to={href} className="block" onClick={onClick}>
                  {menuItem}
                </NavLink>
              </TooltipTrigger>
              <TooltipContent side="right" className="flex items-center gap-2">
                {label}
                {badge && (
                  <Badge variant={badgeVariant} className="h-5 px-1.5 text-xs font-medium">
                    {badge}
                  </Badge>
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <NavLink to={href} className="block" onClick={onClick}>
            {menuItem}
          </NavLink>
        )
      )}
    </div>
  );
};

const SidebarSubItem = ({
  label,
  href,
  badge,
  badgeVariant = 'default',
}: Omit<SidebarItemProps, 'icon' | 'collapsed' | 'children'>) => {
  const location = useLocation();
  const isActive = location.pathname === href;

  return (
    <NavLink
      to={href}
      className={cn(
        "flex items-center justify-between px-2 py-1.5 rounded text-sm",
        "transition-colors duration-200 ease-in-out",
        isActive
          ? "text-primary font-medium bg-primary/5"
          : "text-foreground/60 hover:text-primary hover:bg-primary/5"
      )}
    >
      <span className="truncate">{label}</span>
      {badge && (
        <Badge variant={badgeVariant} className="h-5 px-1.5 text-xs font-medium">
          {badge}
        </Badge>
      )}
    </NavLink>
  );
};

interface SidebarProps {
  onToggle?: (isOpen: boolean) => void;
}

export function Sidebar({ onToggle }: SidebarProps) {
  const { t, i18n } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const isRTL = i18n.language === 'ar';
  const isMobile = useIsMobile();
  const [mobileOpen, setMobileOpen] = useState(false);

  // Get dashboard data for order count
  const { data: dashboardData } = useDashboard();

  // Handle sidebar collapse/expand
  const toggleSidebar = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    onToggle?.(!newState); // onToggle expects isOpen, which is the opposite of collapsed
  };

  // Handle mobile menu toggle
  const toggleMobileMenu = () => {
    setMobileOpen(!mobileOpen);
  };

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && mobileOpen && sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        setMobileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobile, mobileOpen]);

  // Close mobile sidebar on route change
  const location = useLocation();
  useEffect(() => {
    if (isMobile && mobileOpen) {
      setMobileOpen(false);
    }
  }, [location, isMobile, mobileOpen]);

  return (
    <>
      {/* Mobile menu button */}
      {isMobile && (
        <button
          onClick={toggleMobileMenu}
          className="fixed top-4 left-4 z-50 p-2 rounded-md bg-background border border-border shadow-sm"
          aria-label="Toggle menu"
        >
          <MenuIcon size={20} />
        </button>
      )}

      {/* Mobile overlay */}
      {isMobile && (
        <div
          className={cn(
            "fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300",
            mobileOpen ? "opacity-100" : "opacity-0 pointer-events-none"
          )}
          onClick={() => setMobileOpen(false)}
        />
      )}

      {/* Main sidebar */}
      <aside
        ref={sidebarRef}
        className={cn(
          "fixed inset-y-0 z-50 flex flex-col bg-background border-r border-border",
          "transition-all duration-300 ease-in-out",
          isRTL ? "right-0" : "left-0",
          isMobile
            ? cn("w-[280px]", mobileOpen ? "translate-x-0" : isRTL ? "translate-x-full" : "-translate-x-full")
            : cn(collapsed ? "w-[70px]" : "w-[280px]")
        )}
      >
        {/* Header */}
        <div className="flex h-24 items-center justify-between px-4 border-b border-border bg-gradient-to-r from-[#071c44]/5 to-transparent">
          <div className={cn(
            "flex items-center gap-4",
            collapsed && !isMobile ? "justify-center w-full" : "overflow-hidden"
          )}>
            <div className="flex-shrink-0 bg-gradient-to-br from-[#071c44]/10 to-[#0a2454]/5 p-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-[#071c44]/20">
              <img
                src="/images/KALIXLOGO.png"
                alt="KALIX"
                className={cn(
                  "transition-all duration-300 object-contain",
                  collapsed && !isMobile ? "w-12 h-12" : "w-16 h-16"
                )}
              />
            </div>
            {(!collapsed || isMobile) && (
              <div className="flex flex-col">
                <span className="text-2xl font-bold tracking-tight text-[#071c44]">
                  KALIX
                </span>
                <span className="text-sm text-muted-foreground font-medium tracking-wide">
                  Auto Parts Ecosystem
                </span>
              </div>
            )}
          </div>

          {isMobile ? (
            <button
              onClick={() => setMobileOpen(false)}
              className="p-1 rounded-md hover:bg-muted"
              aria-label="Close menu"
            >
              <XIcon size={18} />
            </button>
          ) : (
            <button
              onClick={toggleSidebar}
              className="p-1 rounded-md hover:bg-muted hidden md:flex"
              aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              <ChevronRight
                size={18}
                className={cn(
                  "transition-transform duration-300",
                  collapsed
                    ? isRTL ? "rotate-180" : "rotate-0"
                    : isRTL ? "rotate-0" : "rotate-180"
                )}
              />
            </button>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto py-4 px-3">
          <nav className="space-y-1">
            {/* Dashboard */}
            <SidebarItem
              icon={Home}
              label={t('navigation.dashboard')}
              href="/app/dashboard"
              collapsed={collapsed && !isMobile}
            />

            {/* Products section */}
            <SidebarItem
              icon={Package}
              label={t('navigation.products')}
              href="/app/products"
              collapsed={collapsed && !isMobile}
            >
              <SidebarSubItem
                label={t('navigation.allProducts')}
                href="/app/products"
              />
              <SidebarSubItem
                label={t('navigation.productsTable')}
                href="/app/products-table"
              />

            </SidebarItem>

            {/* Orders */}
            <SidebarItem
              icon={ShoppingBag}
              label={t('navigation.orders')}
              href="/app/orders"
              badge={dashboardData?.totalOrders?.toString() || "0"}
              badgeVariant="secondary"
              collapsed={collapsed && !isMobile}
            />

            {/* Shipments */}
            <SidebarItem
              icon={Truck}
              label={t('navigation.shipments')}
              href="/app/shipments"
              collapsed={collapsed && !isMobile}
            />



            {/* Settings */}
            <SidebarItem
              icon={Settings}
              label={t('navigation.settings')}
              href="/app/settings"
              collapsed={collapsed && !isMobile}
            />
          </nav>
        </div>

        {/* Footer */}
        <div className="border-t border-border p-3">
          {collapsed && !isMobile ? (
            <SidebarItem
              icon={LogOut}
              label={t('navigation.logout')}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                document.getElementById('sidebar-logout-button')?.click();
              }}
              collapsed={true}
            />
          ) : (
            <div id="sidebar-logout-button">
              <LogoutButton />
            </div>
          )}
        </div>
      </aside>
    </>
  );
}
