import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Shield, Eye, EyeOff, Lock, User } from 'lucide-react';
import { useUser } from '@/contexts/UserContext';
import { useAuth } from '@/contexts/AuthContext';
import { signInWithPassword } from '@/services/authService';

export default function AdminLoginPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { setUserRole } = useUser();
  const { refreshSession } = useAuth();

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Handle admin login
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast({
        title: 'Missing Information',
        description: 'Please enter both email and password.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      console.log('🔐 [ADMIN_LOGIN] Attempting admin login...');
      
      // Sign in with email and password
      const result = await signInWithPassword(email, password);

      if (result.error || !result.user) {
        throw new Error(result.error?.message || 'Login failed');
      }

      // Get user profile to verify admin role
      const { getUserProfile } = await import('@/services/authService');
      const { profile, error: profileError } = await getUserProfile(result.user.id);

      if (profileError || !profile) {
        throw new Error('Failed to get user profile');
      }

      // Verify admin role
      if (profile.role !== 'admin') {
        toast({
          title: 'Access Denied',
          description: 'You do not have admin privileges.',
          variant: 'destructive',
        });
        return;
      }

      console.log('✅ [ADMIN_LOGIN] Admin login successful');
      
      // Set admin role
      setUserRole('admin');
      
      // Refresh session
      await refreshSession();
      
      toast({
        title: 'Welcome Admin',
        description: 'Successfully logged in to AROUZ MARKET Admin Panel.',
      });

      // Navigate to admin dashboard
      navigate('/admin/dashboard');
      
    } catch (error) {
      console.error('❌ [ADMIN_LOGIN] Login error:', error);
      toast({
        title: 'Login Failed',
        description: error instanceof Error ? error.message : 'Invalid credentials or access denied.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#071c44] to-[#0a2454] flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="bg-gradient-to-br from-[#fa7b00] to-[#ff8c1a] p-4 rounded-2xl shadow-2xl">
                <img
                  src="/images/KALIXLOGO.png"
                  alt="KALIX"
                  className="h-10 w-10 object-contain filter brightness-0 invert"
                />
              </div>
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-400 rounded-full border-2 border-[#071c44] animate-pulse"></div>
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent tracking-tight">
            KALIX
          </h1>
          <p className="text-blue-200/90 font-medium tracking-wide">
            Admin Control Center
          </p>
        </div>

        {/* Login Form */}
        <Card className="border-0 shadow-2xl">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center text-gray-900">
              Admin Login
            </CardTitle>
            <p className="text-center text-gray-600">
              Enter your admin credentials to access the control panel
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email">Admin Email</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter admin password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10 pr-10"
                    required
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Login Button */}
              <Button
                type="submit"
                className="w-full bg-[#DC2626] hover:bg-[#B91C1C] text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Signing In...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Access Admin Panel
                  </div>
                )}
              </Button>
            </form>

            {/* Footer */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Authorized personnel only. All access is logged and monitored.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Back to Main Site */}
        <div className="text-center mt-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="text-blue-200 hover:text-white hover:bg-blue-800/20"
          >
            ← Back to AROUZ MARKET
          </Button>
        </div>
      </div>
    </div>
  );
}
